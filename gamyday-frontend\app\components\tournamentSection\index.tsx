import React from "react";
import { TournamentSectionProps } from "@/app/types/CommonComponent.types";
import GameCard from "../gameCard";

const TournamentSection: React.FC<TournamentSectionProps> = ({
  tournaments,
  sectionTitle,
  selectedTournamentId,
}) => {
  return (
    <div className="">
      <div className="flex items-center gap-2 mb-5">
        <h1 className="text-2xl font-bold text-white">{sectionTitle}</h1>
      </div>

      <div className="flex items-center gap-x-3 gap-y-6 justify-start flex-wrap">
        {tournaments?.map((tournament) => (
          <GameCard
            key={tournament?.tournament_id}
            tournamentInfo={tournament}
            isOpen={
              tournament?.tournament_id?.toString() === selectedTournamentId
            }
          />
        ))}
      </div>
    </div>
  );
};

export default TournamentSection;
