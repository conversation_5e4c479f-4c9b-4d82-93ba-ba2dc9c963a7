"use client";
import { TournamentConfirmationCardProps } from "@/app/types/CommonComponent.types";
import { formatDate } from "@/app/utils/helper";
import { ArrowRightIcon } from "@heroicons/react/16/solid";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import FullPageLoader from "../common/FullPageLoader";
import { useDispatch, useSelector } from "react-redux";
import { setSelectedTournament } from "@/redux/slices/selectedTournamentSlice";
import BookingSuccess from "../bookingSuccess";
import { toast } from "react-toastify";
import ArrowUturnLeftIcon from "@heroicons/react/24/outline/ArrowUturnLeftIcon";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import api from "@/app/utils/axiosInstance";
import { setUser } from "@/redux/slices/userSlice";
import Link from "next/link";
import Image from "next/image";
import { RootState } from "@/redux/store";

interface CreateBookingParams {
  tournament_id: string;
  slot_id: string;
}

const TournamentConfirmationCard: React.FC<TournamentConfirmationCardProps> = ({
  tournamentDetails,
  selectedTime,
  onBack,
}) => {
  const [bookingDetails, setBookingDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);

  const router = useRouter();
  const dispatch = useDispatch();
  const accessToken = Cookies.get("access_token");
  const refreshToken = Cookies.get("refresh_token");
  const user = useSelector((state: RootState) => state.user);

  const createDirectBooking = async (params: CreateBookingParams) => {
    try {
      setIsLoading(true);
      const response = await api.post(API_ENDPOINTS.BOOKINGS, params);
      if (response?.status === 200) {
        const userRes = await api.get(API_ENDPOINTS.GET_USER);
        if (userRes.status === 200) {
          dispatch(setUser(userRes?.data?.data));
        }
      }
      return response?.data?.data;
    } catch (error: any) {
      setPaymentError(error?.response?.data?.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const onJoinTournamentClick = async () => {
    setIsLoading(true);
    if (!accessToken && !refreshToken) {
      dispatch(setSelectedTournament({ tournamentDetails, selectedTime }));
      router.push(
        `/login?tournament_id=${tournamentDetails?.tournament_id}&slot_id=${selectedTime?.id}`
      );
      return;
    }
    try {
      const bookingData = await createDirectBooking({
        tournament_id: tournamentDetails?.tournament_id as string,
        slot_id: selectedTime?.id.toString(),
      });
      setBookingDetails(bookingData);
      toast.success("Tournament booked successfully!");
      return;
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {!bookingDetails ? (
        <div className=" p-6 w-[700px] relative">
          <button
            onClick={onBack}
            className="absolute -top-5 left-0  bg-red-500 text-[#070b28] rounded-full h-9 w-9 flex items-center justify-center font-semibold cursor-pointer"
          >
            <ArrowUturnLeftIcon className="h-5 w-5 font-semibold" />
          </button>
          {isLoading && <FullPageLoader />}
          <div className="text-center">
{/*             <h2 className="text-white text-5xl font-semibold">
              {tournamentDetails?.name}
            </h2> */}
            <div className="flex justify-between items-center mt-5">
              <p className="text-white text-2xl font-semibold">
                Game: {tournamentDetails?.description}
              </p>
              <p className="text-white text-2xl font-semibold">
                <span>{selectedTime?.formatted_time}, </span>
                {formatDate(tournamentDetails?.date)}
              </p>
            </div>
          </div>
          <div className="mt-4 bg-[#F92C2C] p-4 rounded-sm flex justify-between items-center">
            <h3 className="text-white font-semibold text-3xl">Confirmation:-</h3>
            <p className="text-white text-xl font-medium mt-2">
              <b>Final Amount</b> :
              <span className="font-semibold">
                ₹
                {tournamentDetails?.payment?.final_amount?.toLocaleString(
                  "en-IN"
                )}
              </span>
            </p>
          </div>
{/*           <div className="mt-9 text-white">
            <div className="flex justify-between items-center border-b border-white pb-2 ">
              <div className="w-[370px] grid grid-cols-2 gap-2">
                <p>A. Entry Fee</p>
              </div>
              <p className="flex text-right">
                ₹{tournamentDetails?.payment?.join_price}
              </p>
            </div>
            <div className="flex justify-between items-center mt-4 border-b border-white pb-2">
              <div className="w-[370px] grid grid-cols-2 gap-2">
                <p>B. Platform Fee</p>
                <p>(2% of A)</p>
              </div>
              <p className="text-right">
                ₹
                {tournamentDetails?.payment?.platform_fees?.toLocaleString(
                  "en-IN"
                )}{" "}
              </p>
            </div>
            <div className="flex justify-between items-center mt-4 border-b border-white pb-2">
              <div className="w-[370px] grid grid-cols-2 gap-2">
                <p>C. GST</p>
                <p>(28% of A+B)</p>
              </div>
              <p className="text-right">
                ₹
                {tournamentDetails?.payment?.tax_amount?.toLocaleString(
                  "en-IN"
                )}{" "}
              </p>
            </div>
            <div className="flex justify-between items-center mt-4 border-b border-white pb-2">
              <div className="w-[370px] grid grid-cols-2 gap-2">
                <p>D. Total Amount To Pay</p>
                <p>(A+B+C)</p>
              </div>
              <div className="bg-[#F92C2C] p-2">
                <p className="text-right font-semibold text-lg">
                  To Pay ₹
                  {tournamentDetails?.payment?.final_amount?.toLocaleString(
                    "en-IN"
                  )}
                </p>
              </div>
            </div>
          </div> */}
          {paymentError && (
            <div className="my-4 p-2 text-red-500  rounded">{paymentError}</div>
          )}
          <div className="mt-8 flex items-center justify-between">
            <Link
              href="/my-wallet"
              className="flex items-center group gap-2.5 px-5 py-1.5 rounded-full bg-[#c9ff88] hover:bg-[#141517] hover:text-[#c9ff88] font-semibold text-2xl text-[#131517] border border-[#c9ff88] transition-all duration-200"
            >
              <Image
                src="/icons/wallet.png"
                alt="wallet"
                width={26}
                height={26}
                className="grayscale brightness-0 group-hover:grayscale-0 group-hover:brightness-100 transition-all duration-200"
              />
              <span>₹{user?.wallet?.toLocaleString("en-IN")}</span>
            </Link>
            <button
              onClick={onJoinTournamentClick}
              className="bg-[#c9ff88] flex items-center justify-between text-[#131517] py-2 w-[80%] px-5 max-w-[455px] h-12 rounded-full hover:bg-[#141517] hover:text-[#c9ff88] border border-[#c9ff88] transition-all duration-200"
            >
              <span className="text-base font-semibold">
                {accessToken ? "JOIN TOURNAMENT" : "Login to Join Tournament"}
              </span>
              <ArrowRightIcon
                aria-hidden="true"
                className="h-6 w-6 shrink-0 font-semibold"
              />
            </button>
          </div>
        </div>
      ) : (
        <BookingSuccess data={bookingDetails} />
      )}
    </>
  );
};

export default TournamentConfirmationCard;
