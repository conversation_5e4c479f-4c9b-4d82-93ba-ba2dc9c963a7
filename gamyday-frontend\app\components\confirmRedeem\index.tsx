import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import api from "@/app/utils/axiosInstance";
import { useEffect } from "react";
import Link from "next/link";
import { useState } from "react";
import Loader from "../common/Loader";

interface ConfirmModalContentProps {
  onConfirm: () => void;
  onClose: () => void;
  isRedeemConfirming: boolean;
  redeem_wallet: number;
  hideWarningMessage?: boolean;
}

const ConfirmModalContent = ({
  onConfirm,
  onClose,
  isRedeemConfirming,
  redeem_wallet,
  hideWarningMessage,
}: ConfirmModalContentProps) => {
  const [redeemBreakupData, setRedeemBreakupData] = useState<any>();
  const [isLoading, setIsLoading] = useState(true);

  const fetchRedeemBreakup = async () => {
    setIsLoading(true);
    try {
      const response = await api.post(API_ENDPOINTS.REDEEM_DATA, {
        amount: redeem_wallet,
      });
      if (response.status === 200) {
        setRedeemBreakupData(response?.data?.data);
      }
    } catch (error: any) {
      console.log(error);
      //   setApiError(error?.response?.data?.message);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchRedeemBreakup();
  }, []);
  return (
    <>
      {isLoading ? (
        <div className="min-w-[470px] min-h-[240px] flex items-center justify-center">
          <Loader />
        </div>
      ) : (
        <div className="px-4">
          <h3 className="text-3xl font-bold mb-4 text-white">
            Confirm Redeem Request
          </h3>
          <div className="mb-5 space-y-1">
            <div className="grid grid-cols-2 gap-x-16">
              <div>
                <span className="font-semibold text-lg text-white">
                  Your Redeem Amount
                </span>
              </div>
              <div>
                <span className="font-medium text-lg text-white">
                  ₹{redeemBreakupData?.redeem_amount?.toLocaleString("en-IN")}
                </span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-x-16">
              <div>
                <span className="font-semibold text-sm text-white">
                  Tax Deduction (30%)
                </span>
              </div>
              <div>
                <span className="font-medium text-sm text-white">
                  - ₹{redeemBreakupData?.tax?.toLocaleString("en-IN")}
                </span>
              </div>
            </div>
            <hr className="border borer-white" />

            <div className="grid grid-cols-2 gap-x-16">
              <div>
                <span className="font-semibold text-lg text-white">
                  Final Credit Amount
                </span>
              </div>
              <div>
                <span className=" text-lg font-semibold text-white">
                  ₹{redeemBreakupData?.final_amount?.toLocaleString("en-IN")}
                </span>
              </div>
            </div>
          </div>
          <div className="mt-6 mb-3">
            <p className="text-sm text-white">
              <span className="font-semibold"></span> Please read{" "}
              <Link
                href="/tax-policy"
                className="text-blue-400 hover:underline"
              >
                Tax Policy
              </Link>{" "}
              for more details.
            </p>
            {!hideWarningMessage && (
              <p className="text-sm text-red-500 mt-1">
                <span className="font-semibold">
                  Note: Player can redeem once in a day.
                   </span>
                  <br />
                  <span className="font-semibold">
                   Note: Redeem credits in 2–5 working days.
                  </span>
              </p>
            )}
          </div>
          <div className="flex gap-4">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-md w-full"
            >
              Cancel
            </button>
            <button
              onClick={onConfirm}
              className="flex justify-center w-full rounded-md bg-red-600 px-4 py-2 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
            >
              {isRedeemConfirming ? "Cofirming..." : "Confirm"}
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default ConfirmModalContent;
