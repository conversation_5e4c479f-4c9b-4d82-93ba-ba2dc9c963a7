import { z } from "zod";

const panRegex = new RegExp("^[A-Z]{5}[0-9]{4}[A-Z]{1}$");

export const UpdateProfileFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  upi_id: z.string().min(1, "UPI ID is required"),
  pan_number: z
    .string()
    .min(1, "PAN Number is required")
    .regex(panRegex, "Invalid PAN Number"),
});

export type UpdateProfileFormData = z.infer<typeof UpdateProfileFormSchema>;
