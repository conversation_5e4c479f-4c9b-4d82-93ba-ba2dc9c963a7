"use client";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  TopupWalletFormData,
  TopupWalletFormSchema,
} from "@/app/schema/topupWalletFormSchema";
import { handleNumericInput } from "@/app/utils/helper";

interface TopupWalletFormProps {
  apiError: string | null;
  onSubmit: (data: TopupWalletFormData) => void;
}

const TopupWalletForm: React.FC<TopupWalletFormProps> = ({
  apiError,
  onSubmit,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<TopupWalletFormData>({
    resolver: zodResolver(TopupWalletFormSchema),
  });

  return (
    <div className="w-[450px]">
      <h2 className="text-2xl font-semibold text-white mb-6 text-center">
        Topup your Wallet
      </h2>

      {apiError && (
        <div className="mb-4 p-2 bg-red-500 text-white rounded">{apiError}</div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4">
          <label className="block font-medium leading-6 text-white mb-2">
            Amount (in ₹)
          </label>
          <input
            type="text"
            className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
            placeholder="Enter amount"
            {...register("amount")}
            onChange={handleNumericInput}
          />
          {errors.amount && (
            <p className="text-red-500 text-sm mt-1">{errors.amount.message}</p>
          )}
        </div>

        <div className="mt-6">
          <button
            type="submit"
            disabled={isSubmitting}
            className="flex w-full justify-center rounded-md bg-red-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
          >
            {isSubmitting ? "Processing..." : "Proceed"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default TopupWalletForm;
