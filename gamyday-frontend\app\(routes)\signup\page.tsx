"use client";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { SignupFormData, SignupSchema } from "@/app/schema/signupSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import VerifyOtp from "@/app/components/verifyOtp";
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import { handleNumericInput } from "@/app/utils/helper";
import api from "@/app/utils/axiosInstance";
import { useSearchParams } from "next/navigation";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { withAuthRedirection } from "@/app/utils/withAuthRedirection";

const SignupPage = () => {
  const [apiError, setApiError] = useState<string | null>(null);
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [signupData, setSignupData] = useState<any>({});
  const [showPassword, setShowPassword] = useState(false);

  const searchParamas = useSearchParams();
  const tournament_id = searchParamas.get("tournament_id");
  const slot_id = searchParamas.get("slot_id");

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<SignupFormData>({
    resolver: zodResolver(SignupSchema),
  });

  const onSubmit = async (data: SignupFormData) => {
    const reqData = { ...data, email: data.email.toLowerCase() };
    setApiError(null);
    try {
      const response = await api.post(API_ENDPOINTS.SIGNUP_VERIFY, reqData);
      if (response.status === 200) {
        setSignupData({ ...reqData, otp_id: response?.data?.data?.otp_id });
        setIsOtpSent(true);
      } else {
        setApiError(response?.data?.message);
      }
    } catch (error: any) {
      setApiError(error?.response?.data?.message);
    }
  };

  return (
    <div className="min-h-[calc(100vh-84px)] flex items-center justify-center bg-dark-900 py-8">
      {!isOtpSent ? (
        <div className="bg-dark-800 py-8 px-20 rounded-lg shadow-md w-[580px] bg-[#141517]">
          <h2 className="text-2xl font-semibold text-white mb-6 text-center">
            Create your Account
          </h2>

          {apiError && (
            <div className="mb-4 p-2 bg-red-500 text-white rounded">
              {apiError}
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="mb-4">
              <label className="block font-medium leading-6 text-white mb-2">
                Name
              </label>
              <input
                type="text"
                className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
                placeholder="Enter your name"
                {...register("name")}
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.name.message}
                </p>
              )}
            </div>

            <div className="mb-4">
              <label className="block font-medium leading-6 text-white mb-2">
                Email
              </label>
              <input
                type="email"
                className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
                placeholder="Enter your email"
                {...register("email")}
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>

            <div className="mb-4">
              <label className="block font-medium leading-6 text-white mb-2">
                Mobile
              </label>
              <input
                type="text"
                maxLength={10}
                className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
                placeholder="Enter your mobile number"
                {...register("phone")}
                onInput={handleNumericInput}
              />
              {errors.phone && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.phone.message}
                </p>
              )}
            </div>

            <div className="mb-4">
              <label className="block font-medium leading-6 text-white mb-2">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
                  placeholder="Create a password"
                  {...register("password")}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword((prev) => !prev)}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500"
                >
                  {showPassword ? (
                    <EyeIcon
                      aria-hidden="true"
                      className="ml-2 h-5 w-5 text-black text-medium"
                    />
                  ) : (
                    <EyeSlashIcon
                      aria-hidden="true"
                      className="ml-2 h-5 w-5 text-black text-medium"
                    />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.password.message}
                </p>
              )}
            </div>
            <div className="mb-4">
              <div className="flex items-center">
                <input
                  id="terms-and-conditions"
                  type="checkbox"
                  {...register("termsAndConditions")}
                  className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-600 accent-red-500"
                />
                <label
                  htmlFor="terms-and-conditions"
                  className="ml-3 block text-sm leading-6 text-white"
                >
                  <span>I agree to gamyday&apos;s </span>
                  <Link
                    href={"/terms-and-conditions"}
                    className="hover:underline text-blue-500"
                  >
                    Terms & Conditions
                  </Link>{" "}
                  and
                  <Link
                    href={"/privacy-policy"}
                    className="hover:underline text-blue-500"
                  >
                    {" "}
                    Privacy Policy
                  </Link>
                </label>
              </div>
              {errors.termsAndConditions && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.termsAndConditions.message}
                </p>
              )}
            </div>
            <div className="mt-6">
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex w-full justify-center rounded-md bg-red-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
              >
                {isSubmitting ? "Sending OTP..." : "Send OTP"}
              </button>
            </div>
          </form>

          <div className="mt-4 text-center">
            <p className="font-medium leading-6 text-white">
              Already have an account?{" "}
              <Link
                href={`/login${
                  tournament_id && slot_id
                    ? `?tournament_id=${tournament_id}&slot_id=${slot_id}`
                    : ""
                }`}
                className="text-red-500 hover:underline font-medium hover:text-red-400"
              >
                Login here
              </Link>
            </p>
          </div>
        </div>
      ) : (
        <VerifyOtp setIsOtpSent={setIsOtpSent} signupData={signupData} />
      )}
    </div>
  );
};

export default withAuthRedirection(SignupPage);
