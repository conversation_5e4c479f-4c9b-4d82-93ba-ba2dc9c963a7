export const API_ENDPOINTS = {
  LOGIN: "/auth/login",
  LOGIN_VERIFY: "/auth/login-verify",
  SIGNUP: "/auth/signup",
  SIGNUP_VERIFY: "/auth/signup-verify",
  GENERATE_OTP: "/auth/generate-otp",
  UPDATE_PASSWORD: "/auth/password-update",
  GET_USER: "/user",
  GET_ALL_TOURNAMENTS: "/tournaments",
  GET_TOURNAMENT_DETAILS: (tournamentId: string) =>
    `/tournaments/${tournamentId}`,
  GET_TOURNAMENT_BOOKINGS_DETAILS: (tournamentId: string) =>
    `/tournaments/${tournamentId}/bookings`,
  CREATE_BOOKING_ORDER: "/payments/booking",
  GET_BOOKINGS: (page: number) => `/user/bookings?page=${page}`,
  BOOKINGS: "/user/bookings",
  GET_BOOKING_DETAILS: (bookingId: string) => `/user/bookings/${bookingId}`,
  CREATE_IN_GAME_NAME: (bookingId: string) =>
    `/user/bookings/${bookingId}/in-game-name`,
  GET_BANNERS: "/banners",
  REDEEM: "/redeem",
  GET_ALL_REDEEM: (page: number) => `/redeem?page=${page}`,
  GET_RAISE_REQUEST: (page: number) => `/query?page=${page}`,
  RAISE_REQUEST: "/query",
  RAISE_REQUEST_OPTIONS: "/query-options",
  LEADERBOARD: (page: number) => `/leaderboard?page=${page}`,
  REDEEM_DATA: "/redeem-data",
  REFRESH_TOKEN: "/auth/refresh",
  SOCIAL_ICONS: "/social-icons",
  CREATE_PAYMENT_ORDER: "/payments/order",
  PAYMENT_STATUS: (payment_id: string) => `/payments/order/${payment_id}`,
  GET_WALLET_TRANSACTIONS: (page: number) =>
    `/user/wallet/transactions?page=${page}`,
  GET_YOUTUBE_PARTNERS: (page: number) => `/youtube-partners?page=${page}`,
  REDEEM_WALLET: "/redeem-wallet",
};
