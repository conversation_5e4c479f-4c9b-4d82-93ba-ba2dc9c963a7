"use client";
import { useEffect, useState } from "react";
import { LeaderboardRes } from "@/app/types/CommonComponent.types";
import Loader from "@/app/components/common/Loader";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import ServerError from "@/app/components/common/ServerError";
import Pagination from "@/app/components/pagination";

const tableHeadings = [
  { id: 1, label: "Rank" },
  { id: 2, label: "Name" },
  { id: 3, label: "Total Winnings" },
];

const LeaderboardPage = () => {
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardRes>(
    {} as LeaderboardRes
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<any>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  const totalPages = Math.ceil(leaderboardData?.count / itemsPerPage);

  const fetchLeaderboard = async () => {
    try {
      const response = await api.get(API_ENDPOINTS.LEADERBOARD(currentPage));
      if (response.status === 200) {
        setLeaderboardData(response?.data);
        setIsLoading(false);
      }
    } catch (error: any) {
      setError(error);
    }
  };
  useEffect(() => {
    fetchLeaderboard();
  }, [currentPage]);

  if (error?.response?.status === 500) {
    return <ServerError />;
  }

  return (
    <div className="p-8 w-full">
      <div className=" pt-8">
        {isLoading && <Loader />}

        {!isLoading && leaderboardData?.results?.length === 0 && (
          <p className="text-gray-300 text-lg font-medium">
            No detail available
          </p>
        )}

        {leaderboardData?.results?.length > 0 && (
          <div className="overflow-hidden shadow ring-1 ring-[#707070] rounded-lg w-[1000px] mx-auto">
            <table className="divide-y divide-[#707070] w-full ">
              <thead className="bg-red-500 rounded-lg">
                <tr>
                  {tableHeadings.map((heading, index) => (
                    <th
                      key={heading.id}
                      scope="col"
                      className={`px-3 py-3.5 text-left text-xl font-semibold text-white ${
                        index === 0 ? "pl-12" : ""
                      }`}
                    >
                      {heading.label}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-[#707070]">
                {leaderboardData?.results?.map((leader) => (
                  <tr key={leader?.rank}>
                    <td
                      className={`whitespace-nowrap py-4 pl-16 pr-3 text-white ${
                        leader?.rank === 1
                          ? "font-bold text-2xl"
                          : leader?.rank === 2
                          ? "font-semibold text-xl"
                          : leader?.rank === 3
                          ? "font-semibold text-lg"
                          : "text-base font-semibold"
                      }`}
                    >
                      {leader?.rank}.
                    </td>
                    <td
                      className={`whitespace-nowrap px-3 pl-4 py-4 text-white ${
                        leader?.rank === 1
                          ? "font-bold text-2xl"
                          : leader?.rank === 2
                          ? "font-semibold text-xl"
                          : leader?.rank === 3
                          ? "font-semibold text-lg"
                          : "text-base font-semibold"
                      }`}
                    >
                      {leader?.name}
                    </td>
                    <td
                      className={`whitespace-nowrap px-3 pl-5 py-4 text-white ${
                        leader?.rank === 1
                          ? "font-bold text-2xl"
                          : leader?.rank === 2
                          ? "font-semibold text-xl"
                          : leader?.rank === 3
                          ? "font-semibold text-lg"
                          : "text-base font-semibold"
                      }`}
                    >
                      ₹{leader?.total_winnings.toLocaleString("en-IN")}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      {leaderboardData?.count > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      )}
    </div>
  );
};

export default LeaderboardPage;
