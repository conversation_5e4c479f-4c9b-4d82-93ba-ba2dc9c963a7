import Link from "next/link";
import React from "react";

const TermsConditions = () => {
  return (
    <div className="p-10 space-y-8">
      <h1 className="text-3xl font-bold text-white mb-6">Terms & Conditions</h1>

      <div className="space-y-4">
        <section className="space-y-4">
          <h2 className="text-lg text-white font-semibold">1. General Use</h2>
          <p className="text-white">
            You must be at least 18 years old to use our services. By using this
            website, you represent and warrant that you are at least 18 years of
            age.
          </p>
        </section>

        <section className="space-y-4">
          <h2 className="text-lg font-semibold text-white">
            2. Account Responsibility
          </h2>
          <p className="text-white">
            If you create an account, you are responsible for maintaining the
            confidentiality of your login details. You agree to notify us
            immediately of any unauthorized use of your account.
          </p>
        </section>

        <section className="space-y-4">
          <h2 className="text-lg text-white font-semibold">
            3. Prohibited Conduct
          </h2>
          <p className="text-white">You agree not to:</p>
          <ul className="list-disc pl-6 space-y-2 text-white">
            <li>Engage in any fraudulent or unlawful activity.</li>
            <li>Attempt to hack or disrupt our website or services.</li>
            <li>
              Use the website for any purpose that violates local, state, or
              international laws.
            </li>
          </ul>
        </section>

        <section className="space-y-4">
          <h2 className="text-lg text-white font-semibold">4. I agree to :</h2>
          <ul className="list-disc pl-6 space-y-2 text-blue-500">
            <li>
              <Link href="/refund-policy" className="hover:underline">
                Refund Policy
              </Link>
            </li>
            <li>
              <Link href="/cancellation-policy" className="hover:underline">
                Cancellation Policy
              </Link>
            </li>
            <li>
              <Link href="/tax-policy" className="hover:underline">
                Tax Policy
              </Link>
            </li>
            <li>
              <Link href="/privacy-policy" className="hover:underline">
                Privacy Policy
              </Link>
            </li>
          </ul>
        </section>
      </div>
    </div>
  );
};

export default TermsConditions;
