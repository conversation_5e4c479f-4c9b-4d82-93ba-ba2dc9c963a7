import { configureStore } from "@reduxjs/toolkit";
import userReducer from "../slices/userSlice";
import selectedTournamentReducer from "../slices/selectedTournamentSlice";
import totalUsersCountReducer from "../slices/totalUsersCountSlice";

export const store = configureStore({
  reducer: {
    user: userReducer,
    selectedTournament: selectedTournamentReducer,
    totalUsersCount: totalUsersCountReducer,
  },
  middleware: (getDefaultMiddleware) => getDefaultMiddleware()
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
