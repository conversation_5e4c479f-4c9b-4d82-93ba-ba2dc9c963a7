"use client";
import React, { useState } from "react";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  UpdateInGameNameFormData,
  UpdateInGameNameFormSchema,
} from "@/app/schema/commonSchema";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { UpdateInGameNameResponse } from "@/app/types/CommonComponent.types";

interface UpdateInGameNameFormProps {
  bookingId: string;
  onSuccess: (response: UpdateInGameNameResponse) => void;
  tournamentName: string;
}

const UpdateInGameNameForm: React.FC<UpdateInGameNameFormProps> = ({
  bookingId,
  onSuccess,
  tournamentName,
}) => {
  const [formError, setFormError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<UpdateInGameNameFormData>({
    resolver: zodResolver(UpdateInGameNameFormSchema),
  });

  const onSubmit: SubmitHandler<UpdateInGameNameFormData> = async (
    formData
  ) => {
    setFormError(null);
    try {
      const response = await api.post(
        API_ENDPOINTS.CREATE_IN_GAME_NAME(bookingId),
        formData
      );
      if (response.status === 200) {
        onSuccess(response?.data?.data);
      } else {
        setFormError(response?.data?.message || "An error occurred.");
      }
    } catch (error: any) {
      setFormError(
        error?.response?.data?.message || "An unexpected error occurred."
      );
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <h2 className="text-xl font-semibold text-white mb-4">
        Enter your {tournamentName} name Id
      </h2>
      {formError && (
        <p className="text-red-500 text-base font-semibold mb-3">{formError}</p>
      )}
      <div className="mb-4">
        <input
          type="text"
          placeholder="Enter your Game Name Id"
          className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
          {...register("in_game_name")}
        />
        {errors.in_game_name && (
          <p className="text-red-500 text-sm mt-1">
            {errors.in_game_name.message}
          </p>
        )}
      </div>
      <button
        type="submit"
        disabled={isSubmitting}
        className="flex w-full justify-center rounded-md bg-red-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
      >
        {isSubmitting ? "Updating..." : "Update"}
      </button>
    </form>
  );
};

export default UpdateInGameNameForm;
