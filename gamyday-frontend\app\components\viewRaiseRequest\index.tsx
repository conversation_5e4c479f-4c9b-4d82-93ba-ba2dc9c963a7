import { ViewRaiseRequestProps } from "@/app/types/CommonComponent.types";
import React from "react";

const ViewRaiseRequest: React.FC<ViewRaiseRequestProps> = ({ requestData }) => {
  return (
    <div className="w-[450px]">
      <h2 className="text-2xl font-semibold text-white mb-6 text-center">
        Request No.- {requestData?.id}
      </h2>

      <div>
        <div className="mb-4">
          <span className="block font-medium leading-6 text-white mb-2">
            Status
          </span>
          <span
            className={`font-bold ${
              requestData?.status?.toLowerCase() === "pending"
                ? "text-orange-500"
                : requestData?.status?.toLowerCase() === "completed"
                ? "text-green-500"
                : "text-gray-300"
            }`}
          >
            {requestData?.status ?? "-"}
          </span>
        </div>
        <div className="mb-4">
          <span className="block font-medium leading-6 text-white mb-2">
            Issue
          </span>
          <div className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black">
            {requestData?.issue}
          </div>
        </div>

        {requestData?.issue === "Refunds & Cancellation" && (
          <>
            <div className="mb-4">
              <span className="block font-medium leading-6 text-white mb-2">
                Booking ID
              </span>
              <div className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black">
                {requestData?.booking_id}
              </div>
            </div>
            <div className="mb-4">
              <span className="block font-medium leading-6 text-white mb-2">
                Refund Amount
              </span>
              <div className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black">
                ₹{Number(requestData?.refund_amount).toLocaleString("en-IN")}
              </div>
            </div>
          </>
        )}
        <div className="mb-4">
          <span className="block font-medium leading-6 text-white mb-2">
            Comments
          </span>
          <div className="w-full p-2 max-h-[260px] overflow-y-auto bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black">
            {requestData?.comments ? (
              requestData?.comments
            ) : (
              <span className="text-gray-500">No comment</span>
            )}
          </div>
        </div>
        <div className="mb-4">
          <span className="block font-medium leading-6 text-white mb-2">
            Response
          </span>
          {requestData?.response ? (
            <div className="w-full p-2 max-h-[260px] overflow-y-auto bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black">
              {requestData?.response}
            </div>
          ) : (
            <span className="text-white font-medium">
              *Waiting for response from GamyDay.
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default ViewRaiseRequest;
