import { User } from "@/app/types/User";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const initialState: User = {
  id: 0,
  created_at: "",
  updated_at: "",
  user_id: "",
  name: "",
  email: "",
  phone: "",
  is_active: false,
  wallet: 0,
  redeem_wallet: 0,
  upi_id: "",
  pan_number: "",
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUser: (state: User | null, action: PayloadAction<User | null>) => {
      if (action.payload) {
        return { ...state, ...action.payload };
      }
    },
    clearUser: () => initialState,
  },
});

export const { setUser, clearUser } = userSlice.actions;
export default userSlice.reducer;
