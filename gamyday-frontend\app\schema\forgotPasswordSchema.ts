import { z } from "zod";

export const EmailFormSchema = z.object({
  email: z.string().min(1, "Email is required").email("Invalid email address"),
});

export type EmailFormData = z.infer<typeof EmailFormSchema>;

export const PasswordFormSchema = z
  .object({
    otp: z
      .string()
      .min(4, "OTP must be 4 digits")
      .max(4, "OTP must be 4 digits")
      .regex(/^\d+$/, "OTP must contain only digits"),
    password: z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: z
      .string()
      .min(8, "Confirm password must be at least 8 characters"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    path: ["confirmPassword"],
    message: "Passwords do not match",
  });

export type PasswordFormData = z.infer<typeof PasswordFormSchema>;
