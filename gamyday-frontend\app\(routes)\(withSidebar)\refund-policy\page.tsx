import React from "react";

const RefundPolicy = () => {
  return (
    <div className=" p-10 space-y-8">
      <h1 className="text-3xl font-bold text-white mb-6">Refund Policy</h1>

      <div className="space-y-4">
        <p className="text-lg font-medium text-white">
          Refunds will be issued according to the following terms:
        </p>

        <ul className="list-disc pl-6 space-y-2 text-white">
          <li>We will process refunds for canceled tournaments only.</li>
          <li>
            Refund requests can be raised via &apos;Raise a Tickets&apos;.
          </li>
          <li>
            Refunds are issued to the payment method provided by the requestee.
          </li>
          <li>
            No refunds if joined late in any tournament or unable to join by
            fault of player.
          </li>
          <li>Refund requests are completed within 24 hours.</li>
        </ul>
      </div>
    </div>
  );
};

export default RefundPolicy;
