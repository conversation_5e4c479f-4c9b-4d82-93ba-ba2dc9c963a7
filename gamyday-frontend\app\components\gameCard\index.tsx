"use client";
import React, { useEffect, useState } from "react";
import GameInfoCard from "../gameInfoCard";
import { Modal } from "../modal";
import {
  GameCardProps,
  TimeSlot,
  TournamentDetails,
} from "@/app/types/CommonComponent.types";
import SelectScheduleCard from "../selectScheduleCard";
import TournamentConfirmationCard from "../tournamentConfirmationCard";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { formatDateToDDMMYYYY } from "@/app/utils/helper";

const GameCard: React.FC<GameCardProps> = ({ tournamentInfo, isOpen }) => {
  const [isModalOpen, setModalOpen] = useState(isOpen);
  const [currentStep, setCurrentStep] = useState("gameInfo");

  const [tournamentDetails, setTournamentDetails] =
    useState<TournamentDetails | null>(null);
  const [tournamentBookingsInfo, setTournamentBookingsInfo] = useState<
    any | null
  >(null);
  const [selectedTime, setSelectedTime] = useState<TimeSlot | null>(null);
  const selectedTournament = useSelector(
    (state: RootState) => state.selectedTournament
  );

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (
        selectedTournament.selectedTime &&
        selectedTournament.tournamentDetails
      ) {
        setTournamentDetails(selectedTournament.tournamentDetails);
        setSelectedTime(selectedTournament.selectedTime);
        setCurrentStep("confirmTournament");
      } else {
        fetchData();
      }
    }
  }, [selectedTournament]);

  const onCardClick = () => {
    fetchData();
    fetchTournamentBookingsData();
    setModalOpen(true);
  };
  const onClose = () => {
    setModalOpen(false);
    setCurrentStep("gameInfo");
  };
  const handleJoinNow = () => setCurrentStep("selectSchedule");
  const handleNext = () => setCurrentStep("confirmTournament");
  const handleJoin = () => {
    setModalOpen(false);
    setCurrentStep("gameInfo");
  };

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const res = await api.get(
        API_ENDPOINTS.GET_TOURNAMENT_DETAILS(tournamentInfo?.tournament_id)
      );
      setTournamentDetails(res?.data);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };
  const fetchTournamentBookingsData = async () => {
    try {
      setIsLoading(true);
      const res = await api.get(
        API_ENDPOINTS.GET_TOURNAMENT_BOOKINGS_DETAILS(
          tournamentInfo?.tournament_id
        )
      );
      if (res.status === 200) {
        setTournamentBookingsInfo(res?.data?.data);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <>
      <div
        className="bg-[#c9ff88] flex flex-col items-center rounded-[10px] pb-[4px] pl-2 pr-2 shadow-lg cursor-pointer mt-10 "
        onClick={onCardClick}
      >
        <div className="-mt-10 relative h-[150px] w-[135px]">
          <img
            src={tournamentInfo?.image ?? ""}
            alt={tournamentInfo?.name}
            className="rounded-[10px] object-cover w-full h-full"
          />
        </div>

        <div className="flex items-center flex-col justify-center mt-1.5 gap-1">
          <span className="text-[#131517] text-sm font-semibold leading-none">
            {formatDateToDDMMYYYY(tournamentInfo?.date)}
          </span>
          <span className="mr-1 text-[#131517] text-xl font-semibold leading-none">
            Prize: ₹{tournamentInfo?.prize_pool?.toLocaleString("en-IN")}
          </span>
        </div>
      </div>
      <Modal modalOpen={isModalOpen} handleModalOpen={onClose}>
        {currentStep === "gameInfo" && (
          <GameInfoCard
            tournamentDetails={tournamentDetails}
            onJoinNow={handleJoinNow}
            isLoading={isLoading}
          />
        )}
        {currentStep === "selectSchedule" && (
          <SelectScheduleCard
            tournamentDetails={tournamentDetails}
            selectedTime={selectedTime}
            setSelectedTime={setSelectedTime}
            onNext={handleNext}
            onBack={() => setCurrentStep("gameInfo")}
            tournamentBookingsInfo={tournamentBookingsInfo}
          />
        )}
        {currentStep === "confirmTournament" && (
          <TournamentConfirmationCard
            tournamentDetails={tournamentDetails}
            selectedTime={selectedTime!}
            onJoin={handleJoin}
            onBack={() => setCurrentStep("selectSchedule")}
          />
        )}
      </Modal>
    </>
  );
};

export default GameCard;
