interface PlayerProgressBarProps {
  bookingsCount: number;
  maxPlayers: number;
}

const PlayerProgressBar: React.FC<PlayerProgressBarProps> = ({
  bookingsCount = 0,
  maxPlayers = 0,
}) => {
  const widthPercentage = `${Math.min(
    (bookingsCount / maxPlayers) * 100,
    100
  )}%`;
  const remainingPlayers = maxPlayers - bookingsCount;

  return (
    <div className="pt-2">
      <div className="h-2 bg-gray-700 rounded mt-2">
        <div
          style={{ width: widthPercentage }}
          className="h-full bg-red-600 rounded"
        ></div>
      </div>
      <div className="flex justify-between items-center mt-2">
        <p className="text-base text-white">
          {remainingPlayers} {remainingPlayers > 1 ? "players" : "player"}{" "}
          remaining
        </p>
        <p className="text-base text-white">
          {bookingsCount} {bookingsCount > 1 ? "players" : "player"} joined
        </p>
      </div>
    </div>
  );
};

export default PlayerProgressBar;
