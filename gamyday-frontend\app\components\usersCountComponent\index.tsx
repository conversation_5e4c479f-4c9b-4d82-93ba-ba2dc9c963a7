import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { fetchAndSetUsersCount } from "@/app/utils/helper";

const UsersCountComponent = () => {
  const dispatch = useDispatch<AppDispatch>();
  const usersCount = useSelector(
    (state: RootState) => state.totalUsersCount.count
  );

  useEffect(() => {
    const fetchUsersCount = fetchAndSetUsersCount(dispatch);
    fetchUsersCount();
  }, [dispatch]);

  return (
    <div className="flex items-center gap-2 ml-auto pr-3 pl-10">
      <span className="blinking-dot"></span>
      <span className="text-white font-bold text-lg">PLAYERS : {usersCount}</span>
    </div>
  );
};

export default UsersCountComponent;
