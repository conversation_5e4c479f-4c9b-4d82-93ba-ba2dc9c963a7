"use client";
import { useEffect, useRef, useState } from "react";
import {
  PaymentOrderResponse,
  RedeemData,
  RedeemRes,
  TransactionsResponse,
} from "@/app/types/CommonComponent.types";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import Link from "next/link";
import { Modal } from "@/app/components/modal";
import ConfirmRedeem from "@/app/components/confirmRedeem";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import WithdrawalDetailsForm from "@/app/components/withdrawalDetailsForm";
import { capitalizeFirstLetter, formatDate } from "@/app/utils/helper";
import { toast } from "react-toastify";
import { setUser } from "@/redux/slices/userSlice";
import withAuth from "@/app/utils/withAuth";
import Loader from "@/app/components/common/Loader";
import ServerError from "@/app/components/common/ServerError";
import RedeemDetails from "@/app/components/redeemDetails";
import Pagination from "@/app/components/pagination";
import TopupWalletForm from "@/app/components/topupWalletForm";
import { TopupWalletFormData } from "@/app/schema/topupWalletFormSchema";
import Image from "next/image";
import { TrophyIcon } from "@heroicons/react/24/solid";
import {
  redeemTableHeadings,
  walletTableHeadings,
} from "@/app/constants/tableHeadings";

const RedeemPage = () => {
  const [redeemData, setRedeemData] = useState<RedeemRes>({} as RedeemRes);
  const [walletTransactions, setWalletTransactions] =
    useState<TransactionsResponse>({} as TransactionsResponse);
  const [openConfirmModal, setOpenConfirmModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isRedeemConfirming, setIsRedeemConfirming] = useState(false);
  const [showUpdateWithdrawalInfoModal, setShowUpdateWithdrawalInfoModal] =
    useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showRedeemDetails, setShowRedeemDetails] = useState(false);
  const [selectedRedeem, setSelectedRedeem] = useState<RedeemData | null>(null);
  const [showTopupModel, setShowTopupModel] = useState(false);
  const [error, setError] = useState<any>(null);
  const [topupMethod, setTopupMethod] = useState<"wallet" | "upi" | null>(null);
  const [topupStep, setTopupStep] = useState<
    "selecTopupMethod" | "updateWithdrawalInfo" | "enterAmount" | "confirmTopup"
  >("selecTopupMethod");
  const [activeButton, setActiveButton] = useState<
    "walletTransactions" | "redeemHistory"
  >("walletTransactions");
  const [topupWalletFormData, setTopupWalletFormData] =
    useState<TopupWalletFormData | null>(null);

  const [currentTransactionPage, setCurrentTransactionPage] = useState(1);
  const [currentRedeemPage, setCurrentRedeemPage] = useState(1);
  const itemsPerPage = 10;

  const totalRedeemPages = Math.ceil(redeemData?.count / itemsPerPage);
  const totalTransactionPages = Math.ceil(
    walletTransactions?.count / itemsPerPage
  );

  const dispatch = useDispatch<AppDispatch>();
  const user = useSelector((state: RootState) => state.user);
  const { redeem_wallet, upi_id, pan_number } = user;

  const onRedeemClick = () => {
    if (redeem_wallet < 10) {
      toast.error("Minimum withdrawal amount is ₹10.");
      return;
    }
    if (!upi_id || !pan_number) {
      setOpenConfirmModal(true);
      setShowUpdateWithdrawalInfoModal(true);
    } else {
      setShowConfirmModal(true);
    }
  };
  const onTopmyWalletClick = () => {
    setTopupStep("selecTopupMethod");
    setTopupMethod(null);
    setShowTopupModel(true);
  };
  const confirmRedeem = async () => {
    setIsRedeemConfirming(true);
    try {
      const response = await api.post(API_ENDPOINTS.REDEEM, {
        amount: redeem_wallet,
      });
      if (response.status === 200) {
        const userRes = await api.get(API_ENDPOINTS.GET_USER);
        if (userRes.status === 200) {
          dispatch(setUser(userRes?.data?.data));
        }
        toast.success(
          "Your withdrawal request has been successfully submitted!"
        );
        fetchMyRedeems();
        setShowConfirmModal(false);
        setOpenConfirmModal(false);
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message || "Something went wrong");
      setShowConfirmModal(false);
    } finally {
      setIsRedeemConfirming(false);
    }
  };

  const fetchMyRedeems = async () => {
    try {
      const response = await api.get(
        API_ENDPOINTS.GET_ALL_REDEEM(currentRedeemPage)
      );
      if (response.status === 200) {
        setRedeemData(response?.data);
        setIsLoading(false);
      }
    } catch (error: any) {
      setError(error);
    }
  };

  const fetchWalletTransactions = async () => {
    try {
      const response = await api.get(
        API_ENDPOINTS.GET_WALLET_TRANSACTIONS(currentTransactionPage)
      );
      if (response.status === 200) {
        setWalletTransactions(response?.data);
        setIsLoading(false);
      }
    } catch (error: any) {
      setError(error);
    }
  };

  useEffect(() => {
    fetchMyRedeems();
  }, [currentRedeemPage]);

  useEffect(() => {
    fetchWalletTransactions();
  }, [currentTransactionPage]);

  const onUpdateClick = () => {
    setShowUpdateWithdrawalInfoModal(true);
  };

  const onRowClick = (redeem: any) => {
    setSelectedRedeem(redeem);
    setShowRedeemDetails(true);
  };
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [apiError, setApiError] = useState<string | null>(null);
  const [paymentData, setPaymentData] = useState<PaymentOrderResponse | null>(
    null
  );
  const [showQRCodeModel, setShowQRCodeModel] = useState(false);

  const confirmWalletTopup = async () => {
    setIsRedeemConfirming(true);
    try {
      const res = await api.post(
        API_ENDPOINTS.REDEEM_WALLET,
        topupWalletFormData
      );
      if (res.status === 200) {
        setShowTopupModel(false);
        const userRes = await api.get(API_ENDPOINTS.GET_USER);
        if (userRes.status === 200) {
          dispatch(setUser(userRes?.data?.data));
          fetchWalletTransactions();
          toast.success("Success! You've topped up your wallet successfully.");
        }
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message || "Something went wrong");
      setApiError(
        error?.response?.data?.message || "An error occurred. Please try again."
      );
    } finally {
      setShowTopupModel(false);
      setIsRedeemConfirming(false);
    }
  };

  const onTopupSubmit = async (data: TopupWalletFormData) => {
    setApiError(null);
    if (topupMethod === "wallet") {
      setTopupWalletFormData(data);
      setTopupStep("confirmTopup");
    } else if (topupMethod === "upi") {
      try {
        const res = await api.post(API_ENDPOINTS.CREATE_PAYMENT_ORDER, {
          amount: String(data.amount),
        });
        if (res?.data?.status === "success") {
          setPaymentData(res.data?.data);
          setShowTopupModel(false);
          pollPaymentStatus(res.data?.data?.payment_id);
        }
      } catch (error: any) {
        toast.error(error?.response?.data?.message || "Something went wrong");
        setApiError(
          error?.response?.data?.message ||
            "An error occurred. Please try again."
        );
      }
    }
  };

  useEffect(() => {
    if (paymentData?.payment_url) {
      setShowQRCodeModel(true);
    }
  }, [paymentData?.payment_id]);

  const pollPaymentStatus = (trx_id: string) => {
    console.log("trans", trx_id);
    if (!trx_id) return;

    const interval = setInterval(async () => {
      try {
        const response = await api.get(API_ENDPOINTS.PAYMENT_STATUS(trx_id));
        // Stop polling when payment is completed
        if (response.data.data?.status !== "created") {
          clearInterval(interval);
          setShowQRCodeModel(false);
        }

        if (response.data.data?.status === "success") {
          const userRes = await api.get(API_ENDPOINTS.GET_USER);
          if (userRes.status === 200) {
            dispatch(setUser(userRes?.data?.data));
            fetchWalletTransactions();
            toast.success(
              "Success! You've topped up your wallet successfully."
            );
          }
        }
      } catch (error) {
        console.error("Polling error:", error);
      }
    }, 2000);
  };

  const handleButtonClick = (
    buttonType: "walletTransactions" | "redeemHistory"
  ) => {
    setActiveButton(buttonType);
    setApiError(null);
  };

  const handleTopupMethod = (method: "wallet" | "upi") => {
    if (method === "upi") {
      setTopupMethod("upi");
      setTopupStep("enterAmount");
      return;
    }

    // method === "wallet"
    if (redeem_wallet <= 0) {
      toast.error("You don't have enough balance to top up.");
      return;
    }

    setTopupMethod("wallet");
    setTopupStep(
      !upi_id || !pan_number ? "updateWithdrawalInfo" : "enterAmount"
    );
  };

  if (error?.response?.status === 500) {
    return <ServerError />;
  }

  return (
    <div className="p-8 w-full">
      <div className="flex justify-between items-center mb-8 ">
        <div className="flex items-center gap-8">
          <button
            onClick={onRedeemClick}
            className="flex justify-center rounded-md bg-red-600 px-5 py-3 text-2xl font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
          >
            Redeem Red Wallet: ₹{redeem_wallet.toLocaleString("en-IN")}
          </button>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={onTopmyWalletClick}
            className="flex justify-center items-center gap-1 rounded-md bg-[#c9ff88] px-5 py-3 text-2xl font-semibold leading-6 text-[#131517] shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
          >
            <Image
              src="/icons/wallet.png"
              alt="wallet"
              width={26}
              height={26}
              className="grayscale brightness-0"
            />
            <span>+₹ Top-Up Green Wallet</span>
          </button>
        </div>
      </div>
      <hr className="my-8" />
      <div className="mb-8">
        <div className="flex gap-3 items-center mb-5">
          <h3 className="text-lg font-semibold text-white">
            Withdrawal Details* (Required)
          </h3>
          <button
            onClick={onUpdateClick}
            className="flex justify-center rounded-md bg-red-600 px-3 py-1.5 text-base font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
          >
            Update
          </button>
        </div>
        <div className="space-y-2">
          <div className="flex gap-3">
            <p className="text-white">UPI ID:</p>
            <p className="font-bold text-white">
              {upi_id ?? "Please add your UPI ID"}
            </p>
          </div>
          <div className="flex gap-3">
            <p className="text-white">PAN Number:</p>
            <p className="font-bold text-white ">
              {pan_number ?? "Please add your PAN NUMBER"}
            </p>
          </div>
        </div>
      </div>
      <hr className="my-8" />

      <div>
        <p className="text-base text-white">
          <span className="font-semibold">Notes:</span> Your winnings are liable
          to TDS. The final credited amount will be after tax deduction. Please
          read{" "}
          <Link href="/tax-policy" className="text-blue-400 hover:underline">
            Tax Policy
          </Link>{" "}
          for more details.
        </p>
      </div>
      {isLoading && (
        <div className="py-8">
          <Loader />
        </div>
      )}
      <div className="flex justify-center items-center mt-8">
        <div className="bg-[#7bc127] rounded-full flex">
          <button
            className={`text-[#131517] text-base font-semibold rounded-full py-2.5 px-10 transition-colors ${
              activeButton === "walletTransactions" && "bg-[#c9ff88]"
            }`}
            onClick={() => handleButtonClick("walletTransactions")}
          >
            Green Wallet Transactions
          </button>
          <button
            className={`text-[#131517] text-base font-semibold rounded-full py-2.5 px-10 transition-colors ${
              activeButton === "redeemHistory" && "bg-[#c9ff88]"
            }`}
            onClick={() => handleButtonClick("redeemHistory")}
          >
            Red Wallet Transactions
          </button>
        </div>
      </div>

      {activeButton === "walletTransactions" ? (
        <>
          {!isLoading && walletTransactions?.results?.length === 0 ? (
            <div className="text-gray-100 text-lg font-medium mt-8 text-center">
              No Transactions Available
            </div>
          ) : (
            <div className="pt-8">
              <table className="min-w-full divide-y divide-gray-700">
                <thead>
                  <tr>
                    {walletTableHeadings.map((heading) => (
                      <th
                        key={heading.id}
                        scope="col"
                        className="px-3 py-3.5 text-left text-base font-semibold text-white"
                      >
                        {heading.label}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-800">
                  {walletTransactions?.results?.map((txn) => (
                    <tr
                      key={txn.transaction_id}
                      className="hover:bg-[#c9ff88] hover:cursor-pointer hover:text-[#131517] text-gray-300"
                    >
                      <td className="whitespace-nowrap px-3 py-4 text-base ">
                        {txn.transaction_id}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base ">
                        ₹{Number(txn.amount).toLocaleString("en-IN") ?? "-"}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base ">
                        <span
                          className={`font-bold ${
                            txn.transaction_direction?.toLowerCase() === "debit"
                              ? "text-red-500"
                              : ["credit", "topup"].includes(
                                  txn.transaction_direction?.toLowerCase()
                                )
                              ? "text-green-500"
                              : "text-orange-500"
                          }`}
                        >
                          {capitalizeFirstLetter(txn.transaction_direction) ??
                            "-"}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base">
                        {formatDate(txn?.created_at.split("T")[0])}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base ">
                        {txn.description ?? "-"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          {walletTransactions?.results?.length > 0 && (
            <Pagination
              currentPage={currentTransactionPage}
              totalPages={totalTransactionPages}
              onPageChange={(page) => setCurrentTransactionPage(page)}
            />
          )}
        </>
      ) : (
        <>
          {!isLoading && redeemData?.results?.length === 0 ? (
            <div className="text-gray-100 text-lg font-medium mt-8 text-center">
              No Redeem History Available
            </div>
          ) : (
            <div className="pt-8">
              <table className="min-w-full divide-y divide-gray-700">
                <thead>
                  <tr>
                    {redeemTableHeadings.map((heading) => (
                      <th
                        key={heading.id}
                        scope="col"
                        className="px-3 py-3.5 text-left text-base font-semibold text-white"
                      >
                        {heading.label}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-800">
                  {redeemData?.results?.map((redeem) => (
                    <tr
                      key={redeem.id}
                      className="hover:bg-[#c9ff88] hover:cursor-pointer hover:text-[#131517] text-gray-300"
                      onClick={() => onRowClick(redeem)}
                    >
                      <td className="whitespace-nowrap px-3 py-4 text-base ">
                        {redeem.id}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base ">
                        {redeem.amount}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base ">
                        {redeem.tax_amount}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base ">
                        ₹
                        {Number(redeem.credit_amount).toLocaleString("en-IN") ??
                          "-"}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base ">
                        {redeem.upi_id}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base ">
                        {redeem.pan_number ?? "-"}
                      </td>

                      <td className="whitespace-nowrap px-3 py-4 text-base ">
                        <span
                          className={`font-bold ${
                            redeem.status?.toLowerCase() === "pending"
                              ? "text-orange-500"
                              : redeem.status?.toLowerCase() === "completed"
                              ? "text-green-500"
                              : redeem.status?.toLowerCase() === "rejected"
                              ? "text-red-500"
                              : redeem.status?.toLowerCase() === "topup"
                              ? "text-red-500"
                              : ""
                          }`}
                        >
                          {redeem.status ?? "-"}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-base">
                        {formatDate(redeem?.created_at.split("T")[0])}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {redeemData?.results?.length > 0 && (
            <Pagination
              currentPage={currentRedeemPage}
              totalPages={totalRedeemPages}
              onPageChange={(page) => setCurrentRedeemPage(page)}
            />
          )}
        </>
      )}

      <Modal
        modalOpen={showUpdateWithdrawalInfoModal}
        handleModalOpen={() => setShowUpdateWithdrawalInfoModal(false)}
      >
        <WithdrawalDetailsForm
          handleModal={setShowUpdateWithdrawalInfoModal}
          upi_id={upi_id}
          pan_number={pan_number}
          onUpdateSuccess={() => {
            setShowUpdateWithdrawalInfoModal(false);
            setShowConfirmModal(true);
          }}
          openConfirmModal={openConfirmModal}
        />
      </Modal>

      <Modal
        modalOpen={showConfirmModal}
        handleModalOpen={() => setShowConfirmModal(false)}
      >
        <ConfirmRedeem
          onConfirm={confirmRedeem}
          onClose={() => setShowConfirmModal(false)}
          isRedeemConfirming={isRedeemConfirming}
          redeem_wallet={redeem_wallet}
        />
      </Modal>

      <Modal
        modalOpen={showTopupModel}
        handleModalOpen={() => setShowTopupModel(false)}
      >
        {topupStep === "selecTopupMethod" && (
          <div>
            <h2 className="text-2xl font-semibold text-white mb-6 text-center">
              Select Topup Method
            </h2>
            <div className="flex gap-10">
              <div
                onClick={() => handleTopupMethod("wallet")}
                className="flex-col cursor-pointer w-40 items-center justify-center group gap-2.5 px-5 py-1.5 rounded hover:bg-red-600 bg-[#141517] text-red-500 font-semibold text-2xl hover:text-[#131517] border border-red-500 transition-all duration-200"
              >
                <p className="text-center">Wallet</p>
                <div className="flex items-center justify-center">
                  <TrophyIcon
                    aria-hidden="true"
                    className="h-6 w-6 text-red-500 group-hover:text-[#131517] transition-all duration-200"
                  />
                  <span>₹{user?.redeem_wallet?.toLocaleString("en-IN")}</span>
                </div>
              </div>
              <div
                onClick={() => handleTopupMethod("upi")}
                className="flex-col cursor-pointer w-40 items-center justify-center group gap-2.5 px-5 py-1.5 rounded hover:bg-[#c9ff88] bg-[#141517] text-[#c9ff88] font-semibold text-2xl hover:text-[#131517] border border-[#c9ff88] transition-all duration-200"
              >
                <p className="text-center">UPI</p>
                <div className="flex items-center justify-center">
                  <Image
                    src="/icons/upi.svg"
                    alt="upi"
                    width={60}
                    height={60}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
        {topupStep === "updateWithdrawalInfo" && (
          <WithdrawalDetailsForm
            handleModal={setShowUpdateWithdrawalInfoModal}
            upi_id={upi_id}
            pan_number={pan_number}
            onUpdateSuccess={() => {
              setShowUpdateWithdrawalInfoModal(false);
              setTopupStep("enterAmount");
            }}
            openConfirmModal={true}
          />
        )}
        {topupStep === "enterAmount" && (
          <TopupWalletForm apiError={apiError} onSubmit={onTopupSubmit} />
        )}
        {topupStep === "confirmTopup" && (
          <ConfirmRedeem
            onConfirm={confirmWalletTopup}
            onClose={() => setShowTopupModel(false)}
            isRedeemConfirming={isRedeemConfirming}
            redeem_wallet={topupWalletFormData?.amount as number}
            hideWarningMessage={true}
          />
        )}
      </Modal>

      {/* qr code modal */}
      <Modal
        modalOpen={showQRCodeModel}
        handleModalOpen={() => setShowQRCodeModel(false)}
      >
        {paymentData?.payment_url && (
          <iframe
            ref={iframeRef}
            src={paymentData?.payment_url}
            className="w-[800px] h-[600px] border rounded-lg shadow-lg"
            sandbox="allow-scripts allow-same-origin"
          />
        )}
      </Modal>

      {/* redeem details modal */}
      <Modal
        modalOpen={showRedeemDetails}
        handleModalOpen={() => setShowRedeemDetails(false)}
      >
        <RedeemDetails redeemData={selectedRedeem as RedeemData} />
      </Modal>
    </div>
  );
};

export default withAuth(RedeemPage);