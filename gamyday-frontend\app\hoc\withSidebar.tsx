"use client";
import React from "react";
import Sidebar from "../components/sidebar";
import { useSidebar } from "../context/SidebarContext";

const withSidebar = (WrappedComponent: React.ComponentType) => {
  const ComponentWithSidebar: React.FC = (props) => {
    const { isExpanded } = useSidebar();
    return (
      <div className="flex">
        <Sidebar />
        <main
          className={`flex-1 ${
            isExpanded ? "pl-[320px]" : "pl-[100px]"
          } bg-gradient-to-r from-[#122531] via-[#121f28] to-[#1a2c38]`}
        >
          <WrappedComponent {...props} />
        </main>
      </div>
    );
  };

  return ComponentWithSidebar;
};

export default withSidebar;
